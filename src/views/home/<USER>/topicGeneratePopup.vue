<template>
  <van-popup v-model="show" :round="true" @closed="handleClose">
    <div class="popupBox">
      <van-field
        v-model="userRequest"
        label-width="10vw"
        label="需求"
        type="textarea"
        placeholder="请输入新专题需求描述"
        :error-message="showAlert ? '新专题需求描述不能为空' : ''"
        rows="5"
        autosize
        required
        clearable
      >
      </van-field>
      <div v-show="!isGenerating && !!topicInfo" class="successMessage">
        生成成功！
        <div><a @click="gotoNewTopic">跳转</a>至生成的新专题，或更改需求后重新生成</div>
      </div>
      <van-button
        size="small"
        :type="isGenerating ? 'info' : 'primary'"
        round
        :loading="isGenerating"
        loading-text="生成中..."
        @click.stop="generateNewTopic"
        >{{ !topicInfo ? '开始生成' : '重新生成' }}</van-button
      >
    </div>
  </van-popup>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  name: 'TopicGeneratePopup',
  data() {
    return {
      show: false,
      userRequest: '',
      isGenerating: false,
      topicInfo: null,
      moduleInfo: null,
      showAlert: false,
      moduleMap: new Map()
    }
  },
  watch: {
    userRequest(val) {
      if (val) this.showAlert = false
    }
  },
  created() {
    getAction('cockpit/workbench/scCockpitWorkbench/allList').then((res) => {
      const topicList = res.result[0].childTopicList
      this.moduleInfo = topicList
        .map((topic) => {
          return JSON.parse(topic.topicJson)
        })
        .filter((item) => !!item)
        .map((topic) => {
          return topic.components.filter((component) => !!component.cptOption.attribute.isModule)
        })
        .flat()
        .map((module) => {
          this.moduleMap.set(module.id, module) // 建立id和模块信息对应的哈希表， 便于基于接口返回的id数组构建新专题
          return {
            id: module.id,
            moduleName: module.cptOption.attribute.name,
            moduleDes: module.cptOption.attribute.moduleDes || module.cptOption.attribute.name // 若模块无描述，则默认为模块名称
          }
        })
    })
  },
  methods: {
    handleClose() {
      // 清空表单
      this.userRequest = ''
      this.isGenerating = false
      this.topicInfo = null
      this.moduleInfo = null
      this.showAlert = false
    },
    generateNewTopic() {
      if (!this.userRequest) {
        this.showAlert = true
        return
      }
      this.showAlert = false
      this.isGenerating = true
      this.generateTopic()
      console.log(this.userRequest)
      setTimeout(() => {
        this.isGenerating = false
        this.topicInfo = {}
      }, 1000)
    },
    gotoNewTopic() {
      this.$router.push('topicGenerate')
    }
  }
}
</script>
<style lang="less" scoped>
.van-popup {
  z-index: 9999 !important;
}
.popupBox {
  width: 700px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  position: relative;
}
::v-deep .van-cell__value {
  background: #f0f2f5;
  border-radius: 10px;
  padding: 5px 10px;
}
.successMessage {
  color: #6cc98b;
  font-size: 24px;
  max-width: 600px;
  margin: 10px 0;
  text-align: left;
  font-weight: bold;
  a {
    font-style: italic;
    text-decoration: underline;
    text-indent: 5px;
    padding: 0 5px;
    color: #3498db;
  }
}
@media screen and (min-width: 600px) {
  ::v-deep {
    .van-field__control {
      height: 120px !important;
      font-size: 2.6vw;
    }
    .van-cell__title {
      font-size: 2.6vw;
    }
    .van-field__error-message {
      font-size: 2.6vw;
    }
  }
  .van-button {
    height: 4.2vw;
    width: 50%;
    font-size: 2.6vw;
    margin-left: 25%;
  }
  .successMessage {
    font-size: 2.6vw;
  }
}
</style>

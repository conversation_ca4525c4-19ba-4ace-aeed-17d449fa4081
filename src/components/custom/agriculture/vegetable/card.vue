<template>
  <div v-if="!!data" class="card-box">
    <!-- <div class="time">{{ data.index_time }}</div> -->
    <popover
      v-if="!!options.length"
      :container="'.popover-vegetable'"
      :actions="options"
      :default-option="selectedOption"
      class="toppopover"
      @selectedOption="handleSelectedTime"
    ></popover>
    <div class="title">{{ data.index_name }}</div>
    <div class="value">
      <span>{{ data.value1 }}</span>
      <span>{{ data.unit1 }}</span>
    </div>
    <div class="right-flex-container">
      <div v-if="!!data.index_name2" class="rect1">
        <div class="title">{{ data.index_name2 }}</div>
        <div class="value">
          <img src="@/assets/img/arrow-up.png" alt="" />
          <div>{{ data.value2 }}</div>
          <div>{{ data.unit2 }}</div>
        </div>
      </div>
      <div v-if="data.index_name3 === '居全国第'" class="rect2">
        <div class="title">{{ data.index_name3 }}</div>
        <div class="rank" :class="data.value3 == 1 ? 'rank1' : 'rank-else'">{{ data.value3 }}</div>
      </div>
      <div v-if="data.index_name3 === '占全国的'" class="rect2">
        <div class="title">{{ data.index_name3 }}</div>
        <div class="rate">{{ data.value3 }}%</div>
      </div>
    </div>
    <div v-if="isPad" class="icon"></div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import popover from '@/components/selector.vue'

export default {
  components: {
    popover
  },
  data() {
    return {
      data: null,
      options: [],
      selectedOption: '',
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  },
  async mounted() {
    await this.getOptions()
    this.getData()
  },
  methods: {
    async getOptions() {
      const time = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_scclgk_kp_time`
      )
      this.options = time.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_scclgk_kp`
      )
      if (res.success) {
        this.data = res.result.filter((item) => item.index_time === this.selectedOption)[0]
      }
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.card-box {
  width: 702px;
  height: 302px;
  background: url('../image/card-bg-1.png') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  .icon {
    height: 200px;
    width: 300px;
    background: url('../image/card-bottom-icon4.png') no-repeat center center / 100% 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1;
  }
  .toppopover {
    position: absolute;
    right: -5px;
    top: 20px;
  }
  > .title {
    position: absolute;
    top: 34px;
    left: 28px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 32px;
    text-align: left;
    font-style: normal;
  }
  > .value {
    position: absolute;
    top: 140px;
    left: 28px;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    span {
      &:nth-child(1) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 68px;
        color: #ffffff;
        line-height: 95px;
        text-align: left;
        font-style: normal;
      }
      &:nth-child(2) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        margin-left: 10px;
      }
    }
  }
  .right-flex-container {
    position: absolute;
    width: 50%;
    left: 340px;
    top: 100px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    z-index: 2;
  }
  .rect1,
  .rect2 {
    height: 150px;
    width: 160px;
    background: url('../image/sccl-icon1.png') no-repeat center center / 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #ffffff;
      line-height: 32px;
      text-align: center;
      font-style: normal;
    }
    .value {
      display: flex;
      align-items: baseline;
      justify-content: center;
      img {
        height: 30px;
        width: 20px;
      }
      div:nth-child(2) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 48px;
        color: #dd2929;
        line-height: 48px;
        text-align: left;
        font-style: normal;
      }
      div:nth-child(3) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32px;
        color: #dd2929;
        line-height: 32px;
        text-align: left;
        font-style: normal;
      }
    }
    .rank {
      height: 45px;
      width: 45px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 29px;
      color: #ffffff;
      line-height: 41px;
      text-align: center;
      font-style: normal;
      display: inline-block;
      &.rank1 {
        background: url('../image/sortBg1.png') no-repeat center center / 100% 100%;
      }
      &.rank-else {
        background: url('../image/sortBg2.png') no-repeat center center / 100% 100%;
      }
    }
    .rate {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #f6ff00;
      line-height: 28px;
      text-align: left;
      font-style: normal;
    }
  }
}
::v-deep {
  .van-button {
    width: calc(var(--base-size) * 20);
    height: calc(var(--base-size) * 6);
    font-size: calc(var(--base-size) * 2.6);
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 200px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: calc(var(--base-size) * 2.4);
    line-height: calc(var(--base-size) * 6);
    width: 100%;
    height: 60px;
    padding: 8px;
  }
  .el-input {
    width: 230px;
  }
  .el-input__inner {
    // background-color: transparent;
    background: url('/src/assets/img/date_bg2.png') no-repeat center/100% calc(var(--base-size) * 5.6);
    color: white;
    border: 0;
    text-align: center;
    font-size: calc(var(--base-size) * 2.4);
    padding-bottom: 8px;
  }
}
</style>

<template>
  <div class="total-container">
    <div v-if="!!cardData" class="card-box">
      <popover
        v-if="!!options.length"
        :container="'.popover-ruralRevitalization'"
        :actions="options"
        :default-option="selectedOption"
        class="toppopover"
        @selectedOption="handleSelectedTime"
      ></popover>
      <!-- <div class="time">{{ cardData[1].field_value }}</div> -->
      <div class="row-1">{{ cardData.index_name }}</div>
      <div class="row-2">
        <span class="num">{{ cardData.index_value }}</span>
        <span class="unit">{{ cardData.index_unit }}</span>
      </div>
      <div class="icon"></div>
    </div>
    <div v-if="!!cardBelowData" class="card-below-box">
      <titleWithIcon title="国有企业助力乡村振兴行动结对共建情况"></titleWithIcon>
      <div class="popover-ruralRevitalization2">
        <popover
          v-if="!!options2.length"
          :container="'.popover-ruralRevitalization2'"
          :actions="options2"
          :default-option="selectedOption2"
          @selectedOption="handleSelectedTime2"
        ></popover>
      </div>

      <div class="below-content">
        <div v-for="item in cardBelowData" :key="item.id" class="item-box">
          <div class="title">{{ item.field_name }}</div>
          <div class="value">
            <span class="num">{{ item.field_value }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import titleWithIcon from '../components/titleWithIcon.vue'
import { getAction } from '@/api/manage'
import popover from '@/components/selector.vue'

export default {
  components: {
    titleWithIcon,
    popover
  },
  data() {
    return {
      cardData: null,
      cardBelowData: null,
      options: [],
      selectedOption: '',
      options2: [],
      selectedOption2: '',
      topicId: '',
      topicOrder: 0,
      topicCode: 'A38A03'
    }
  },
  async mounted() {
    await this.getOptions()
    this.getCardData()
    this.getCardBelowData()
  },
  methods: {
    async getOptions() {
      const time = await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_nync_xczxtdsj_kp_time',
        {}
      )
      if (time.success) {
        this.options = time.result.map((item) => {
          return {
            text: item.index_time
          }
        })
        this.selectedOption = this.options[0].text
      }
      const time2 = await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_nync_xczx_gjqk_time',
        {}
      )
      if (time2.success) {
        this.options2 = time2.result.map((item) => {
          return {
            text: item.index_time
          }
        })
        this.selectedOption2 = this.options2[0].text
      }
      const topic = this.$store.state.DataSourceList.filter((item) => item.topicCode == this.topicCode)[0]
      this.topicId = topic.topicId
      this.topicOrder = topic.order
    },
    async getCardData() {
      const res = await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL +
          `/topic/data/listAllBySql/ydd_nync_xczxtdsj_kp?indexTime=${this.selectedOption}`
      )
      if (res.success) this.cardData = res.result[0]
    },
    async getCardBelowData() {
      const res = await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL +
          `/topic/data/listAllBySql/ydd_nync_xczx_gjqk?indexTime=${this.selectedOption2}`
      )
      if (res.success) this.cardBelowData = res.result
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getCardData()
    },
    handleSelectedTime2(time) {
      this.selectedOption2 = time
      this.getCardBelowData()
      getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown`,
        {
          id: this.topicId,
          name: time,
          indexTime: time
        }
      ).then((res) => {
        if (res) {
          const result = res.result[0]
          this.$eventBus.$emit('changeTime', {
            dpDataTime: result.dp_data_time,
            dataSource: result.data_source,
            frequency: result.frequency,
            stSyncTime: result.st_sync_time,
            index: this.topicOrder
          })
          const newobj = {
            value1: result.data_source,
            value2: result.st_sync_time,
            value3: result.frequency,
            value4: result.dp_data_time
          }
          this.$emit('selectedOption', newobj)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  .card-box {
    position: relative;
    width: 702px;
    height: 302px;
    background: url('../image/card-bg-2.png');
    background-size: 100% 100%;
    .icon {
      height: 200px;
      width: 300px;
      background: url('../image/card-bottom-icon1.png') no-repeat center center / 100% 100%;
      position: absolute;
      bottom: 0;
      right: 0;
    }
    ::v-deep {
      .van-button {
        width: calc(var(--base-size) * 20);
        height: calc(var(--base-size) * 6);
        font-size: calc(var(--base-size) * 2.6);
      }
      .van-popover__content {
        overflow: scroll;
        max-height: 300px;
        width: 200px;
        border-radius: 10px;
      }
      .van-popover__action {
        font-size: calc(var(--base-size) * 2.4);
        line-height: calc(var(--base-size) * 6);
        width: 100%;
        height: 60px;
        padding: 8px;
      }
      .el-input {
        width: 200px;
      }
      .el-input__inner {
        // background-color: transparent;
        background: url('/src/assets/img/date_bg3.png') no-repeat center/100% calc(var(--base-size) * 5.6);
        color: white;
        border: 0;
        text-align: center;
        font-size: calc(var(--base-size) * 2.4);
        // padding-bottom: 5px;
      }
    }

    .toppopover {
      position: absolute;
      right: -5px;
      top: 20px;
    }
    .time {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      margin-left: 11px;
      background: url('@/assets/img/date_bg.png') no-repeat center/100% 48px;
      align-items: center;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      right: -5px;
      width: 192px;
      height: 48px;
      top: 38px;
      padding-bottom: 8px;
    }
    .row-1 {
      position: absolute;
      top: 32px;
      left: 29px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #ffffff;
      line-height: 45px;
      text-align: left;
      font-style: normal;
    }
    .row-2 {
      position: absolute;
      top: 140px;
      left: 28px;
      .num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 68px;
        color: #ffffff;
        line-height: 95px;
        text-align: left;
        font-style: normal;
      }
      .unit {
        margin-left: 13px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        text-align: right;
        font-style: normal;
      }
    }
    .row-3-copy1 {
      position: absolute;
      top: 237px;
      left: 30px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 32px;
      text-align: left;
      font-style: normal;
    }
  }
  .card-below-box {
    padding: 0 28px 0 28px;
    position: relative;

    .popover-ruralRevitalization2 {
      display: flex;
      justify-content: end;
    }
    .below-content {
      width: 100%;
      height: 260px;
      margin-top: 33px;
      margin-left: auto;
      background-image: url('../image/xczx-bg-icon.png'), url('../image/xczx-bg-1.png');
      background-repeat: no-repeat, no-repeat;
      background-size: 88px 82px, 100% 100%;
      background-position: center center, center center;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      > .item-box {
        .title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 32px;
          color: #ffffff;
          line-height: 45px;
          text-align: center;
          font-style: normal;
        }
        .num {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 48px;
          color: #ffffff;
          line-height: 67px;
          text-align: right;
          font-style: normal;
        }
        .unit {
          margin-left: 12px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 24px;
          color: #ffffff;
          line-height: 33px;
          text-align: left;
          font-style: normal;
        }
        &:nth-child(1) {
          width: 96px;
          height: 127px;
          position: absolute;
          left: 72px;
          top: 67px;
        }
        &:nth-child(2) {
          width: 160px;
          height: 127px;
          position: absolute;
          right: 39px;
          top: 67px;
        }
      }
    }
    ::v-deep {
      .van-button {
        // width: 260px;
        padding: 10px 20px;
        height: 60px;
        font-size: 26px;
      }
      .van-popover__content {
        overflow: scroll;
        max-height: 300px;
        width: 280px;
        border-radius: 10px;
      }
      .van-popover__action {
        font-size: 24px;
        line-height: 60px;
        width: 100%;
        height: 60px;
        padding: 8px;
      }
    }
    ::v-deep {
      .el-input {
        width: 310px;
      }
    }
  }
}
</style>

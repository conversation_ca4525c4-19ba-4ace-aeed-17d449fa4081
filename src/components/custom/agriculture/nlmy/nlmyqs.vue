<template>
  <div>
    <div class="chart-container">
      <barLine :chart-data="chartData"></barLine>
    </div>
    <orderlist></orderlist>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barLine from '../components/barLine.vue'
import orderlist from './orderlist.vue'
export default {
  components: { barLine, orderlist },
  data() {
    return {
      chartData: {
        data: [],
        column: ['name', 'value1', 'value2'],
        columnName: { name: '', value1: '农林渔牧业生产总值', value2: '同比增速' },
        unitList: { name: '', value1: '亿元', value2: '%' },
        type: ['bar', 'line'],
        colors: ['#0192f8', '#39c56a'],
        xLabelLineStrNum: 5
        // rotateX: 45
        // 一次显示条数占总数百分比
        // showProp: 30
      }
    }
  },
  mounted() {
    this.getChartData()
  },
  methods: {
    async getChartData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_nlmyy_kp_qs`
      )
      if (res.success)
        this.chartData.data = res.result.map((item) => {
          return {
            name: item.index_time,
            value1: item.cz,
            value2: item.tb
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.chart-container {
  height: 500px;
  margin-top: -24px;
  div {
    height: 100%;
  }
  ::v-deep .echarts-container {
    height: 100%;
  }
}
</style>

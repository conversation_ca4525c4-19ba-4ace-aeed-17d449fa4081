<template>
  <div class="total-container">
    <div v-if="!!data" class="card-box">
      <popover
        v-if="!!options.length"
        :container="'.popover-nlmy'"
        :actions="options"
        :default-option="selectedOption"
        class="toppopover"
        @selectedOption="handleSelectedTime"
      ></popover>
      <div class="row-1">
        <div>{{ data.index_name }}</div>
        <span>{{ data.value1 }}</span>
        <span>{{ data.unit1 }}</span>
      </div>
      <div class="row-2">
        <div class="left">
          <span>{{ data.name2 }}</span>
          <img src="@/assets/img/arrow-up.png" alt="" />
          <span>{{ data.value2 }}</span>
          <span>{{ data.unit2 }}</span>
        </div>
        <div v-if="!!data.name3" class="vertical-line"></div>
        <div v-if="!!data.name3" class="right">
          <span>{{ data.name3 }}</span>
          <span>{{ data.value3 }}</span>
          <span>{{ data.unit3 }}</span>
        </div>
      </div>
      <div class="icon"></div>
    </div>
  </div>
</template>
<script>
import { getNlmy } from '@/api/agriculture'
import titleWithIcon from '../components/titleWithIcon.vue'
import popover from '@/components/selector.vue'
import { getAction } from '@/api/manage'

export default {
  components: {
    titleWithIcon,
    popover
  },
  inject: ['addBg'],
  data() {
    return {
      data: null,
      dataBelow: null,
      options: [],
      selectedOption: '',
      isPad: window.matchMedia('(min-width: 600px)').matches,
      topicId: ''
    }
  },

  async mounted() {
    this.topicId = this.$store.state.DataSourceList[3].topicId
    await this.getTime()
    this.getCardData()
    this.getCardBelowData()
  },
  methods: {
    async getTime() {
      const time = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_nlmyy_kp_time`
      )
      this.options = time.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getCardData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_nlmyy_kp?indexTime=${
          this.selectedOption
        }`
      )
      this.data = res.result[0]
    },
    async getCardBelowData() {
      const res = await getNlmy(this.selectedOption)
      this.dataBelow = [...res.result]
    },
    getChangeTime() {
      getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown`,
        {
          id: this.topicId,
          name: this.selectedOption,
          indexTime: this.selectedOption
        }
      ).then((res) => {
        if (res) {
          //index用于父组件修改哪个子组件下的数据来源
          //其他的参数是该组件获取四组数据,让父组件修改该组件的四组数据
          const result = res.result[0]
          this.$eventBus.$emit('changeTime', {
            dpDataTime: result.dp_data_time,
            dataSource: result.data_source,
            frequency: result.frequency,
            stSyncTime: result.st_sync_time,
            index: 3
          })

          //供pad使用
          const newobj = {
            value1: result.data_source,
            value2: result.st_sync_time,
            value3: result.frequency,
            value4: result.dp_data_time
          }
          this.$emit('selectedOption', newobj)
        }
      })
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getChangeTime()
      this.getCardData()
    }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  .card-box {
    width: 702px;
    height: 302px;
    background: url('../image/card-bg-1.png') no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    .icon {
      height: 200px;
      width: 300px;
      background: url('../image/card-bottom-icon2.png') no-repeat center center / 100% 100%;
      position: absolute;
      bottom: 0;
      right: 0;
    }
    .toppopover {
      position: absolute;
      right: -5px;
      top: 20px;
    }
    .row-1 {
      position: absolute;
      top: 50px;
      left: 20px;
      > div {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32px;
        color: #ffffff;
        line-height: 45px;
        text-align: left;
        font-style: normal;
      }
      span {
        &:nth-child(2) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 68px;
          color: #ffffff;
          line-height: 95px;
          text-align: left;
          font-style: normal;
        }

        &:nth-child(3) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .row-2 {
      position: absolute;
      width: 80%;
      top: 200px;
      left: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        span {
          &:nth-child(1) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #ffffff;
            line-height: 40px;
            text-align: left;
            font-style: normal;
          }
          &:nth-child(3) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 48px;
            color: #dd2929;
            line-height: 48px;
            text-align: left;
            font-style: normal;
          }
          &:nth-child(4) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #dd2929;
            line-height: 40px;
            text-align: left;
            font-style: normal;
          }
        }
        img {
          height: 30px;
          width: 20px;
          margin-left: 3px;
        }
      }
      .vertical-line {
        width: 1px;
        height: 50px;
        border: 1px solid;
        border-image: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.63),
            rgb(255, 255, 255),
            rgba(255, 255, 255, 0.62),
            rgba(255, 255, 255, 0)
          )
          2 2;
      }
      .right {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 36px;
        text-align: left;
        font-style: normal;
        span:nth-child(2) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 48px;
          color: #ffffff;
          line-height: 48px;
          text-align: left;
          font-style: normal;
          margin-left: 3px;
        }
      }
    }
  }
}
::v-deep {
  .van-button {
    width: calc(var(--base-size) * 20);
    height: calc(var(--base-size) * 6);
    font-size: calc(var(--base-size) * 2.6);
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 200px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: calc(var(--base-size) * 2.4);
    line-height: calc(var(--base-size) * 6);
    width: 100%;
    height: 60px;
    padding: 8px;
  }
  .el-input {
    width: 250px;
  }
  .el-input__inner {
    // background-color: transparent;
    background: url('/src/assets/img/date_bg3.png') no-repeat center/100% calc(var(--base-size) * 5.6);
    color: white;
    border: 0;
    text-align: center;
    font-size: calc(var(--base-size) * 2.4);
    padding-bottom: 5px;
  }
}
</style>

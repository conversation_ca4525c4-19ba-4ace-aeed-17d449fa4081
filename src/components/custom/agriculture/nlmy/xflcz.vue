<template>
  <div>
    <div class="line"></div>
    <div class="tabs">
      <div
        v-for="(item, i) in tabs"
        :key="i"
        :class="{ tab: true, active: item.name === active }"
        @click="tabClick(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="chart-container">
      <barLine :chart-data="chartData"></barLine>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barLine from '../components/barLine.vue'
export default {
  name: 'Xflcz',
  components: { barLine },
  data() {
    return {
      active: '种植业',
      tabs: [
        { name: '种植业', data: null },
        { name: '林业', data: null },
        { name: '畜牧业', data: null },
        { name: '渔业', data: null },
        { name: '农林牧渔专业及其辅助活动', data: null }
      ],
      chartData: {
        data: [],
        column: ['name', 'value1', 'value2'],
        columnName: { name: '', value1: '种植业产值', value2: '同比增速' },
        unitList: { name: '', value1: '亿元', value2: '%' },
        type: ['bar', 'line'],
        colors: ['#0192f8', '#39c56a'],
        xLabelLineStrNum: 5
      }
    }
  },
  mounted() {
    this.getChartData()
  },
  methods: {
    tabClick(item) {
      this.active = item.name
      this.chartData.columnName.value1 = item.name + '产值'
      this.getChartData()
    },
    async getChartData() {
      getAction(`${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_nlmy_xflczjzsqk`, {
        category: this.active
      }).then((res) => {
        if (res.success) {
          this.chartData.data = res.result.map((item) => {
            return {
              name: item.year,
              value1: item.production,
              value2: item.growth_rate
            }
          })
        }
      })
      //   const res2 = await getAction(
      //     `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_nlmy_xflczjzsqk`,
      //     { category: this.active }
      //   )
      //   console.log(res2)
    }
  }
}
</script>
<style lang="scss" scoped>
.tabs {
  //   display: flex;
  //   justify-content: space-between;
  //   flex-wrap: wrap;
  margin: 25px 0;
  .tab {
    display: inline-block;
    min-width: 130px;
    padding: 6px 24px;
    border-radius: 30px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    background: #f5f5f5;
    font-size: 28px;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    margin: 0 20px 20px 0;
  }

  .active {
    color: white;
    background: #0190ff;
  }
}
.chart-container {
  height: 500px;
  margin-top: -24px;
  div {
    height: 100%;
  }
  ::v-deep .echarts-container {
    height: 100%;
  }
}
</style>

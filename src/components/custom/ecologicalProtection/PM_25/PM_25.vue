<template>
  <div class="productbox">
    <div class="title">PM2.5平均浓度</div>
    <div class="contBox">
      <div class="baseCard">
        <div class="lt">
          <div class="title2">1-11月PM2.5平均浓度</div>
          <div class="val">35<span>微克/立方米同比</span></div>
        </div>
        <div class="rt">
          <img src="../img/yuanhu2.png" alt="" />
          <div class="cont">
            <div class="btm">同比</div>
            <div class="top">
              <span>持平</span>
              <!-- <span class="unit">{{ textData[1].unit }}</span> -->
            </div>
          </div>
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="1" :show-data-zoom="false" />
      </div>
      <sourceData :source-data="sourceData" class="sourcedata" :dp-type="false" :category="category" />
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../common/barChart.vue'
import sourceData from '../common/sourceData.vue'

export default {
  components: {
    barChart,
    sourceData
  },
  props: {
    sourceData: {
      type: Object,
      default: () => {}
    },
    category: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      chartData: [
        { year: '2024年1月', ratio: 36.18, growth_yoy: 0.68 },
        { year: '2024年1-2月', ratio: 42.41, growth_yoy: 1.23 },
        { year: '2024年1-3月', ratio: 45.36, growth_yoy: 1.33 },
        { year: '2024年1-4月', ratio: 47.36, growth_yoy: 1.44 },
        { year: '2024年1-5月', ratio: 55.36, growth_yoy: 2.17 }
      ],
      chartConfig: {
        yAxis0_name: '单位：微克/立方米',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${e[1].seriesName}: ${e[1].value}微克/立方米</br>
          ${e[3].seriesName}: ${e[3].value}%
          `
        }
      },
      isPad: window.matchMedia('(min-width: 600px)').matches,
      textData: null
    }
  }
}
</script>

<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 402px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .btmBar {
      width: 100%;
      height: 450px;
      margin-top: 30px;
    }

    .baseCard {
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30px;
      background: linear-gradient(#4e94ff 0%, #5ec0ff 100%);
      .lt {
        .title2 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 28px;
          color: #ffffff;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
        .val {
          margin-top: 60px;
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #ffffff;
          line-height: 27px;
          text-align: left;
          font-style: normal;
          span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 16px;
            text-align: left;
            font-style: normal;
          }
        }
      }
      .rt {
        position: relative;
        img {
          width: 208px;
          height: 122px;
        }
        .cont {
          position: absolute;
          top: 50px;
          left: 70px;
          display: flex;
          flex-direction: column;
          align-items: center;
          .top {
            margin-top: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 36px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            .unit {
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              font-size: 24px;
              color: #666666;
              line-height: 33px;
              text-align: left;
              font-style: normal;
            }
          }
          .btm {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }
  .sourcedata {
    margin: 30px auto 0;

    width: 100%;
  }
}
</style>

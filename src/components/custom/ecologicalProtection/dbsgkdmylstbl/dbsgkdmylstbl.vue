<template>
  <div class="productbox">
    <div class="title">地表水国控断面优良水体比例</div>
    <div class="contBox">
      <div class="baseCard">
        <div class="time">2025年</div>
        <div class="title2">地表水国控断面优良水体比例</div>
        <div class="valbox">
          <div class="ltval">
            <div class="num">83.7</div>
            <div class="unit">%</div>
          </div>
          <div class="ctval">
            <div class="desc"><span>优于</span>国家下达目标</div>
            <!-- <img src="@/assets/img/arrow-up.png" alt="" /> -->
            <div class="value">15.1<span>百分点</span></div>
          </div>
        </div>
      </div>
      <div class="btmBar">
        <barChart
          :chart-data="chartData"
          :std-num="stdNum"
          :ratio="ratio"
          :name3="name3"
          :year="year"
          :total-ratio="totalRatio"
        />
      </div>
      <sourceData :source-data="sourceData" class="sourcedata" :one-line="isPad" :dp-type="false" :category="category" />
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../common/barChart2.vue'
import sourceData from '../common/sourceData.vue'

export default {
  components: {
    barChart,
    sourceData
  },
  props: {
    sourceData: {
      type: Object,
      default: () => {}
    },
    category: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      chartData: [],
      stdNum: [],
      ratio: [],
      totalRatio: [],
      name3: [],
      year: [],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      textData: {}
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_ylstbl_jbqk'
      ).then((res) => {
        if (res.success) {
          this.textData = {
            name: res.result[0].field_name,
            value: res.result[0].field_value,
            unit: '%'
          }
        }
      })
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_ylstbl_month'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.stdNum = []
          this.ratio = []
          this.name3 = []
          this.year = []
          res.result.forEach((item) => {
            this.stdNum.push(item.std_num)
            this.ratio.push(item.ratio)
            this.name3.push(item.name3)
            this.year.push(item.year)
          })

          this.totalRatio = this.ratio.map((item, index) => item - (this.stdNum[index] || 0))
          // console.log(this.name3);
          // console.log(res.result);
          // console.log('++++++');
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .btmBar {
      margin-top: 60px;
      width: 100%;
      height: 450px;
      margin-top: 30px;
    }
    .baseCard {
      width: 100%;
      height: 200px;
      background: linear-gradient(to right, #4e93ff 0%, #5ec0ff 100%);
      //   background: url('../img/baseCardbg.png') center/100% 100% no-repeat;
      border-radius: 10px;
      padding: 35px 30px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 24px;
      .time {
        background: url('../img/timebg.png') center/100% 100% no-repeat;
        width: 110px;
        height: 75px;
        position: absolute;
        right: -4px;
        top: -8px;
        z-index: 3;
        text-align: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 65px;
        font-style: normal;
      }
      .title2 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
      .valbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ltval {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
        }

        .rtval {
          display: flex;
          align-items: baseline;
          margin-top: 10px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 27px;
          text-align: left;
          font-style: normal;
          span {
            display: inline-block;
            width: 50px;
            height: 48px;
            background: url('../img/numbg.png') center/100% 100% no-repeat;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            color: #ffffff;
            line-height: 45px;
            text-align: center;
            font-style: normal;
          }
        }
        .ctval {
          display: flex;
          align-items: baseline;
          margin-right: 10px;
          .desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
            span {
              color: #de242c;
            }
          }
          img {
            width: 25px;
            height: 35px;
            margin: 0 5px;
          }
          .value {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: #de242c;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            span {
              color: white;
              font-size: 24px;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
  .sourcedata {
    margin: 30px auto 0;
    width: 100%;
  }
}
</style>

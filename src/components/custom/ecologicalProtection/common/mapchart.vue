<template>
  <div class="center_box">
    <div ref="mapChart" class="map-chart"></div>
    <mapDialog :position="position" :city-data="cityData" :show-dialog="showDialog" :map-dialog-city="mapDialogCity" />
  </div>
</template>
<script>
import * as echarts from 'echarts'
import geoJSON from '@/static/json/china.json'
// import { getAction, postAction } from '@/api/request'
import mapDialog from './mapDialog.vue'
export default {
  name: 'CenterMap',
  components: {
    mapDialog
  },
  data() {
    return {
      mapChart: null,
      cityData: null,
      position: { top: '23%', right: '33%' },
      showDialog: false,
      mapDialogCity: '',
      centerData: [
        { name: '北京', value: [] },
        { name: '天津', value: [] },
        { name: '河北', value: [] },
        { name: '山西', value: [] },
        { name: '内蒙古', value: [] },
        { name: '辽宁', value: [] },
        { name: '吉林', value: [] },
        { name: '黑龙江', value: [] },
        { name: '上海', value: [] },
        { name: '江苏', value: [] },
        { name: '浙江', value: [] },
        { name: '安徽', value: [] },
        { name: '福建', value: [] },
        { name: '江西', value: [] },
        {
          name: '山东',
          value: [118.5009, 37.4512],
          selected: true,
          position: { top: '43%', right: '33%' }
        },
        { name: '河南', value: [] },
        { name: '湖北', value: [] },
        { name: '湖南', value: [] },
        { name: '重庆', value: [] },
        { name: '四川', value: [] },
        { name: '贵州', value: [] },
        { name: '云南', value: [] },
        { name: '西藏', value: [] },
        { name: '陕西', value: [] },
        { name: '甘肃', value: [] },
        { name: '青海', value: [] },
        { name: '宁夏', value: [] },
        { name: '新疆', value: [] },
        { name: '广东', value: [] },
        { name: '广西', value: [] },
        { name: '海南', value: [] },
        { name: '香港', value: [] },
        { name: '澳门', value: [] },
        { name: '台湾', value: [] }
      ]
    }
  },
  async mounted() {
    this.mapDialogCity = '山东'
      this.cityData = [
        { name: '已建成国家级生态文明建设示范区', value: 32, unit: '个' },
        { name: '全国排名第', value: 5, unit: '' },
        { name: '"绿水青山就是金山银山"实践创新基地', value: 11, unit: '个' },
        { name: '全国排名第', value: 2, unit: '' },
        { name: '美丽河湖', value: 4, unit: '个' },
        { name: '全国排名第', value: 2, unit: '' },
        { name: '美丽海湾', value: 4, unit: '个' },
        { name: '全国排名第', value: 2, unit: '' },
      ]
  
    this.showDialog = true
    this.initMapEchart()
  },
  methods: {
    initMapEchart(count) {
      let that = this
      if (!this.mapChart) {
        this.mapChart = echarts.init(this.$refs.mapChart)
      }
      echarts.registerMap('china', geoJSON)
      this.mapChart.setOption({
        tooltip: {
          show: false
        },
        geo: {
          map: 'china',
          zoom: 1.7,
          top: '30%',
          roam: false,
          itemStyle: {
            normal: {
              borderColor: 'transparent',
              borderWidth: 0,
              color: 'rgb(0, 167, 250)'
            }
          },
          regions: [
            {
              name: '南海诸岛',
              itemStyle: {
                areaColor: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 10, 52, 0)'
              }
            }
          ],
          label: {
            show: false,
            color: '#fff'
          },
          emphasis: {
            disabled: true
          },
          z: 2
        },
        series: [
          {
            type: 'map',
            map: 'china',
            zoom: 1.7,
            top: '30%',
            label: {
              show: false,
              fontFamily: 'SourceHanSansCN',
              fontSize: 9,
              color: 'black'
            },
            itemStyle: {
              normal: {
                areaColor: '#a6daff',
                borderWidth: 1,
                borderColor: '#ebf7ff'
              }
            },
            selectedMode: 'multiple',
            select: {
              disabled: false,
              itemStyle: {
                areaColor: '#69c1ff'
              },
              label: {
                fontFamily: 'SourceHanSansCN',
                fontSize: 9,
                color: 'black'
              }
            },
            zlevel: 0,
            data: this.centerData
          },
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            itemStyle: {
              color: '#ff6862'
            },
            symbol: 'path://M0,1.4 L10.6424229,19.1767442 L21.5860465,1.4 L10.6424229,4.85376744 L0,1.4 Z',
            symbolSize: [12, 12],
            z: 9999,
            data: this.centerData
          }
        ]
      })

      // this.mapChart.on('click', function (params) {
      //   if (!params.data.allData) {
      //     that.showMapDialog = false
      //     return
      //   }
      //   that.showMapDialog = true
      //   // if (params.event.event.offsetX > 1020) {
      //   //   that.position.left = params.event.event.offsetX - 900
      //   // } else {
      //   //   that.position.left = params.event.event.offsetX + 150
      //   // }
      //   // if (params.event.event.offsetY > 820) {
      //   //   that.position.top = params.event.event.offsetY - 300
      //   // } else {
      //   // }
      //   that.position = params.data.position
      //   const data = params.data.allData
      //   that.mapDialogCity = data.district_name
      //   that.cityData = [
      //     { name: '完成水利投资', value: data.tz, unit: data.unit },
      //     { name: '全国排名第', value: data.order, unit: '' }
      //   ]
      // })
    }
  }
}
</script>
<style lang="scss" scoped>
.center_box {
  position: relative;
  height: 100%;
  width: 100%;
  .map-chart {
    height: 100%;
    width: 100%;
  }
}
</style>

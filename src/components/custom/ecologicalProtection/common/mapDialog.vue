<template>
  <div class="dialogBox" :style="{ right: position.right, top: position.top }">
    <div class="name">
      {{ mapDialogCity }}
    </div>
    <div class="dialogcontBox">
      <div v-for="(item, i) in cityData" :key="i" class="contItem">
        <div class="lt">{{ item.name }}</div>
        <div class="rt">
          {{ item.value }}<span class="unit">{{ item.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Dialog',
  props: {
    position: {
      type: Object,
      default: () => {}
    },
    cityData: {
      type: Array,
      default: () => []
    },
    showDialog: {
      type: Boolean,
      default: false
    },
    mapDialogCity: {
      type: String,
      default: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.dialogBox {
  min-width: 227px;
  // background: url('../img/ct_dia_1.png') center / 100% 100% no-repeat;
  background: rgba(74, 154, 219, 0.8);
  padding: 10px;
  position: absolute;
  z-index: 10;
  padding: 5px 15px;
  transition: all 0.1s ease;
  .name {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 18px;
    color: #ffffff;
    line-height: 25px;
    text-align: left;
    font-style: normal;
  }
  .dialogcontBox {
    padding: unset !important;
    .contItem {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .rt {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #ffffff;
        line-height: 25px;
        text-align: left;
        font-style: normal;
        span {
          font-size: 18px;
        }
      }
      .lt {
        max-width: 12em;
        margin-right: 30px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
        line-height: 25px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>

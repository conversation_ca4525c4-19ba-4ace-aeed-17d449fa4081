<template>
  <div class="box">
    <div class="title">环境质量持续向好的基础脆弱</div>

    <div class="box_title">
      <img src="../img/titlebg4.png" alt="" />
      <div>⼤⽓环境⽅⾯</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox">
      <div class="toptext">受气象条件总体不利、烟花爆竹集中燃放等因素影响，</div>
      <div class="toptext">完成国家下达目标存在极大不确定性。</div>
      <div class="ctval">
        <div class="name">累计重污染天数距国家要求的年 度控制⽬标余量</div>
        <div class="num">0.3<span>天</span></div>
        <div class="desc">优良天数比例<span>未达</span>年度序时进度</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  components: {},
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 35px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .box_title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 75px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 140px 36px 36px;
    position: relative;
    .toptext {
      width: 100%;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }
    .ctval {
      width: 100%;
      padding: 30px 30px 72px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(45deg, #4e93ff 0%, #5ebfff 100%);
      border-radius: 16px;
      margin-top: 30px;
      position: relative;
      .name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        width: 14em;
      }
      .num {
        font-family: D-DIN, D-DIN;
        font-weight: bold;
        font-size: 68px;
        color: #ffffff;
        line-height: 27px;
        text-align: right;
        font-style: normal;
        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 16px;
          text-align: left;
          font-style: normal;
        }
      }
      .desc {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        border-radius: 0 0 16px 16px;
        height: 62px;
        background-color: #3782ff;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 62px;
        text-align: center;
        font-style: normal;
        span {
          color: #2ce2b7;
        }
      }
    }
  }
}
</style>
